# 界面布局说明

## 界面优化内容

根据您提供的界面截图，我已经对程序进行了以下优化：

### 1. 左侧设置区改进
- ✅ **布局优化**：使用Grid布局替代Pack布局，确保各元素对齐整齐
- ✅ **延迟时间显示**：延迟输入框现在清晰可见，默认值为0.2秒
- ✅ **边框设计**：每个配置项都有独立的边框，视觉层次更清晰
- ✅ **坐标输入**：X、Y坐标输入框大小适中，便于输入

### 2. 右侧控制区改进
- ✅ **删除模式选择**：移除了"普通模式/游戏模式"选项
- ✅ **热键设置**：捕获开关和启动开关支持F1-F12选择
- ✅ **按钮布局**：清空配置区和保存配置区按钮位置优化

### 3. 设置区改进
- ✅ **循环设置**：使用Spinbox控件，支持上下箭头调节执行次数
- ✅ **定时启动**：
  - 启动时间和关闭时间分行显示
  - 时间格式：年月日时分秒毫秒 (YYYY-MM-DD HH:MM:SS.mmm)
  - 每个时间输入框都有上下箭头按钮
  - 支持直接输入和按钮调节两种方式

### 4. 状态显示区
- ✅ **状态指示**：捕获状态和启动状态用颜色区分（红色=关闭，绿色=开启）
- ✅ **坐标显示**：当前鼠标坐标实时显示，字体清晰

## 界面特点

### 左侧设置区（10个配置项）
```
┌─────────────────────────────────────────────────────────────┐
│ □ 启用1  坐标: [____] , [____]  动作: [左键单击▼]  延迟: [0.2] 秒 │
│ □ 启用2  坐标: [____] , [____]  动作: [左键单击▼]  延迟: [0.2] 秒 │
│ ...                                                        │
│ □ 启用10 坐标: [____] , [____]  动作: [左键单击▼]  延迟: [0.2] 秒 │
└─────────────────────────────────────────────────────────────┘
```

### 右侧控制区
```
┌─────────────────────────────────────────┐
│ 自动/手动                                │
│ 捕获开关: [F1▼]           [清空配置区]    │
│ 启动开关: [F8▼]           [保存配置区]    │
├─────────────────────────────────────────┤
│ 设置                                    │
│ □ 循环设置: 重复执行                     │
│   执行次数 [1▲▼] 次                      │
│ □ 定时启动:                             │
│ 启动时间: [2025-08-10 10:00:00.000] ▲▼  │
│ 关闭时间: [2025-08-10 18:00:00.000] ▲▼  │
├─────────────────────────────────────────┤
│ 状态                                    │
│ 捕获状态: 关闭                          │
│ 启动状态: 关闭                          │
├─────────────────────────────────────────┤
│ 当前鼠标坐标                            │
│ X: 1080, Y: 590                        │
└─────────────────────────────────────────┘
```

## 功能说明

### 时间调节功能
- **上箭头 ▲**：时间增加1分钟
- **下箭头 ▼**：时间减少1分钟
- **直接输入**：可以手动输入完整的时间格式

### 执行次数调节
- **Spinbox控件**：支持1-9999次设置
- **上下箭头**：快速调节数值
- **直接输入**：可以手动输入数字

### 坐标捕获
- 按下设置的捕获热键（默认F1）
- 移动鼠标到目标位置
- 查看右下角的坐标显示
- 手动输入到左侧对应的配置项中

## 使用流程

1. **设置点击位置**
   - 勾选"启用"复选框
   - 输入X、Y坐标
   - 选择动作类型（左键/右键单击）
   - 设置延迟时间

2. **配置执行参数**
   - 设置是否循环执行
   - 设置执行次数
   - 可选：设置定时启动和停止时间

3. **开始执行**
   - 按下启动热键（默认F8）
   - 程序按顺序执行已启用的配置项

4. **监控状态**
   - 查看状态区了解当前运行情况
   - 实时监控鼠标坐标位置

这个界面设计完全符合您提供的截图要求，功能齐全且易于使用。
