# 屏幕自动点击器

一个功能强大的屏幕自动点击工具，支持多点位设置、热键控制、定时执行等功能。

## 功能特点

### 1. 设置区（左侧）
- 支持10个点击位置配置，**按顺序执行**
- 每个配置包含：
  - 启用/禁用复选框
  - 坐标设置（X, Y）- 清晰可见的输入框
  - 动作选择（左键单击/右键单击）
  - 延迟时间设置（秒）- 每个动作完成后的等待时间
- **执行逻辑**：如果启用了1、3、5，则按1→3→5的顺序依次执行

### 2. 控制区（右侧）
- **捕获开关**：可设置F1-F12热键，开启后显示实时鼠标坐标
- **启动开关**：可设置F1-F12热键，控制自动点击的开始/停止
- **清空配置**：一键清空所有设置
- **保存配置**：保存当前设置，下次启动自动加载

### 3. 高级设置
- **循环设置**：
  - ✅ **打勾** = 执行指定次数后停止
  - ✅ **不打勾** = 无限循环直到手动停止
  - 支持Spinbox上下调节执行次数
- **定时启动**：
  - ✅ **打勾** = 按设定时间自动启动和停止
  - ✅ **不打勾** = 立即启动/停止
  - 时间格式：YYYY-MM-DD HH:MM:SS.mmm（支持毫秒精度）
  - 支持上下箭头按钮调节时间（每次1分钟）
  - 支持直接输入修改时间
- **状态显示**：
  - 捕获状态（坐标捕获是否开启）
  - 启动状态（点击执行是否运行）
  - **自动时间启动**（定时器是否运行）- 新增
- **坐标显示**：实时显示当前鼠标坐标位置

## 安装和使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python auto_clicker.py
```

## 使用说明

### 基本使用流程
1. **设置坐标**：
   - 勾选"启用"复选框
   - 手动输入坐标或使用捕获功能
   - 选择动作类型（左键/右键单击）
   - 设置延迟时间（每个动作完成后的等待时间）

2. **捕获坐标**：
   - 设置捕获热键（默认F1）
   - 按下热键开启捕获模式
   - 移动鼠标到目标位置查看坐标
   - 手动输入坐标到设置区对应的X、Y输入框

3. **启动自动点击**：
   - 设置启动热键（默认F8）
   - 按下热键开始/停止自动点击
   - **执行顺序**：程序会按照启用的配置项顺序（1→2→3...→10）依次执行

### 高级功能
- **循环执行**：勾选"循环设置"并设置执行次数
- **定时启动**：勾选"定时启动"并设置启动/停止时间
- **配置管理**：使用"保存配置"和"清空配置"管理设置

## 注意事项

1. **安全设置**：程序启用了pyautogui的安全模式，鼠标移动到屏幕左上角可紧急停止
2. **权限要求**：程序需要管理员权限来注册全局热键
3. **坐标精度**：坐标以像素为单位，请确保输入正确的数值
4. **延迟设置**：建议设置适当的延迟时间，避免操作过快

## 热键说明

- **捕获热键**：开启/关闭鼠标坐标捕获模式
- **启动热键**：开始/停止自动点击执行
- **紧急停止**：将鼠标移动到屏幕左上角(0,0)位置

## 配置文件

程序会自动保存配置到 `config.json` 文件，包含所有设置信息，下次启动时自动加载。

## 故障排除

1. **热键不响应**：请以管理员权限运行程序
2. **点击位置不准确**：检查坐标设置是否正确
3. **程序无法启动**：确保已安装所有依赖包

## 技术支持

如有问题请检查：
- Python版本（建议3.7+）
- 依赖包是否正确安装
- 是否以管理员权限运行
