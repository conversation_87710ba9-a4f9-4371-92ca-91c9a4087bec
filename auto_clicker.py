# -*- coding: utf-8 -*-
import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui
import keyboard
import threading
import time
import json
import os
from datetime import datetime, timedelta

class AutoClicker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("屏幕自动点击器")
        self.root.geometry("900x650")  # 增大窗口尺寸
        self.root.resizable(False, False)
        
        # 状态变量
        self.capturing = False
        self.running = False
        self.timer_running = False  # 定时器是否运行中
        self.timer_thread = None    # 定时器线程
        self.current_pos = (0, 0)
        self.config_file = "config.json"
        
        # 配置数据
        self.configs = []
        for i in range(10):
            self.configs.append({
                'enabled': False,
                'x': 0,
                'y': 0,
                'action': '左键单击',
                'delay': 0.2
            })
        
        # 设置变量
        self.repeat_enabled = tk.BooleanVar()
        self.repeat_count = tk.IntVar(value=1)
        self.timer_enabled = tk.BooleanVar()
        self.capture_hotkey = tk.StringVar(value="F1")
        self.start_hotkey = tk.StringVar(value="F8")
        
        # 定时器变量 (年月日时分秒毫秒格式)
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        self.start_time_var = tk.StringVar(value=current_time)
        self.stop_time_var = tk.StringVar(value=current_time)
        
        self.setup_ui()
        self.load_config()
        self.setup_hotkeys()
        
        # 启动鼠标位置监控
        self.monitor_thread = threading.Thread(target=self.monitor_mouse, daemon=True)
        self.monitor_thread.start()
        
    def setup_ui(self):
        # 主框架
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧设置区 - 增大宽度
        left_frame = tk.LabelFrame(main_frame, text="设置区", width=580)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        left_frame.pack_propagate(False)

        # 右侧控制区 - 调整宽度
        right_frame = tk.LabelFrame(main_frame, text="控制区", width=300)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=(5, 0))
        right_frame.pack_propagate(False)
        
        self.setup_left_panel(left_frame)
        self.setup_right_panel(right_frame)
        
    def setup_left_panel(self, parent):
        # 配置项
        self.config_widgets = []
        for i in range(10):
            frame = tk.Frame(parent, relief=tk.RIDGE, bd=1, height=35)
            frame.pack(fill=tk.X, pady=2, padx=5)
            frame.pack_propagate(False)  # 防止框架收缩

            # 启用复选框 - 调整宽度让数字可见
            enabled_var = tk.BooleanVar()
            enabled_cb = tk.Checkbutton(frame, text=f"启用{i+1}", variable=enabled_var, width=10)
            enabled_cb.place(x=5, y=5)

            # 坐标输入 - 调整位置
            tk.Label(frame, text="坐标:").place(x=90, y=8)
            x_entry = tk.Entry(frame, width=8)
            x_entry.place(x=125, y=6)
            tk.Label(frame, text=",").place(x=185, y=8)
            y_entry = tk.Entry(frame, width=8)
            y_entry.place(x=195, y=6)

            # 动作选择 - 调整位置
            tk.Label(frame, text="动作:").place(x=260, y=8)
            action_var = tk.StringVar(value="左键单击")
            action_combo = ttk.Combobox(frame, textvariable=action_var,
                                      values=["左键单击", "右键单击"], width=10, state="readonly")
            action_combo.place(x=295, y=6)

            # 延迟设置 - 调整位置，"秒"字往右移
            tk.Label(frame, text="延迟:").place(x=420, y=8)
            delay_entry = tk.Entry(frame, width=6)
            delay_entry.place(x=450, y=6)
            delay_entry.insert(0, "0.2")  # 默认值
            tk.Label(frame, text="秒").place(x=510, y=8)  # 往右移动

            self.config_widgets.append({
                'enabled_var': enabled_var,
                'x_entry': x_entry,
                'y_entry': y_entry,
                'action_var': action_var,
                'delay_entry': delay_entry
            })
        
    def setup_right_panel(self, parent):
        # 自动/手动控制
        control_frame = tk.LabelFrame(parent, text="自动/手动")
        control_frame.pack(fill=tk.X, pady=5)

        # 捕获开关
        capture_frame = tk.Frame(control_frame)
        capture_frame.pack(fill=tk.X, pady=2)
        tk.Label(capture_frame, text="捕获开关:").pack(side=tk.LEFT)
        capture_combo = ttk.Combobox(capture_frame, textvariable=self.capture_hotkey,
                                   values=[f"F{i}" for i in range(1, 13)], width=5, state="readonly")
        capture_combo.pack(side=tk.LEFT, padx=5)
        tk.Button(capture_frame, text="清空配置区", command=self.clear_config).pack(side=tk.RIGHT)

        # 启动开关
        start_frame = tk.Frame(control_frame)
        start_frame.pack(fill=tk.X, pady=2)
        tk.Label(start_frame, text="启动开关:").pack(side=tk.LEFT)
        start_combo = ttk.Combobox(start_frame, textvariable=self.start_hotkey,
                                 values=[f"F{i}" for i in range(1, 13)], width=5, state="readonly")
        start_combo.pack(side=tk.LEFT, padx=5)
        tk.Button(start_frame, text="保存配置区", command=self.save_config).pack(side=tk.RIGHT)
        
        # 设置区
        settings_frame = tk.LabelFrame(parent, text="设置")
        settings_frame.pack(fill=tk.X, pady=5)

        # 循环设置
        repeat_frame = tk.Frame(settings_frame)
        repeat_frame.pack(fill=tk.X, pady=2)
        tk.Checkbutton(repeat_frame, text="循环设置:", variable=self.repeat_enabled).pack(side=tk.LEFT)
        #tk.Label(repeat_frame, text="重复执行").pack(side=tk.LEFT, padx=(10, 0))

        # 执行次数设置 - 修复显示问题
        count_frame = tk.Frame(repeat_frame)
        count_frame.pack(side=tk.LEFT, padx=(10, 0))
        tk.Label(count_frame, text="执行次数").pack(side=tk.LEFT)
        self.repeat_spinbox = tk.Spinbox(count_frame, from_=1, to=100000, textvariable=self.repeat_count,
                                        width=6, state="normal")
        self.repeat_spinbox.pack(side=tk.LEFT, padx=5)
        tk.Label(count_frame, text="次").pack(side=tk.LEFT)

        # 定时启动
        timer_frame = tk.Frame(settings_frame)
        timer_frame.pack(fill=tk.X, pady=2)
        tk.Checkbutton(timer_frame, text="定时启动:", variable=self.timer_enabled).pack(anchor=tk.W)

        # 启动时间
        start_time_frame = tk.Frame(settings_frame)
        start_time_frame.pack(fill=tk.X, pady=2)
        tk.Label(start_time_frame, text="启动时间:").pack(side=tk.LEFT)
        self.start_time_entry = tk.Entry(start_time_frame, textvariable=self.start_time_var, width=25)
        self.start_time_entry.pack(side=tk.LEFT, padx=5)

        # 启动时间调节按钮
        start_btn_frame = tk.Frame(start_time_frame)
        start_btn_frame.pack(side=tk.LEFT, padx=3)
        start_up_btn = tk.Button(start_btn_frame, text="▲", width=6, height=1,
                               command=lambda: self.adjust_time('start', 1))
        start_up_btn.pack(side=tk.TOP)
        start_down_btn = tk.Button(start_btn_frame, text="▼", width=6, height=1,
                                 command=lambda: self.adjust_time('start', -1))
        start_down_btn.pack(side=tk.TOP)

        # 关闭时间
        stop_time_frame = tk.Frame(settings_frame)
        stop_time_frame.pack(fill=tk.X, pady=2)
        tk.Label(stop_time_frame, text="关闭时间:").pack(side=tk.LEFT)
        self.stop_time_entry = tk.Entry(stop_time_frame, textvariable=self.stop_time_var, width=25)
        self.stop_time_entry.pack(side=tk.LEFT, padx=5)

        # 关闭时间调节按钮
        stop_btn_frame = tk.Frame(stop_time_frame)
        stop_btn_frame.pack(side=tk.LEFT, padx=3)
        stop_up_btn = tk.Button(stop_btn_frame, text="▲", width=3, height=1,
                              command=lambda: self.adjust_time('stop', 1))
        stop_up_btn.pack(side=tk.TOP)
        stop_down_btn = tk.Button(stop_btn_frame, text="▼", width=3, height=1,
                                command=lambda: self.adjust_time('stop', -1))
        stop_down_btn.pack(side=tk.TOP)
        
        # 状态显示
        status_frame = tk.LabelFrame(parent, text="状态")
        status_frame.pack(fill=tk.X, pady=5)
        
        self.capture_status_label = tk.Label(status_frame, text="捕获状态: 关闭", fg="red")
        self.capture_status_label.pack(anchor=tk.W)

        self.running_status_label = tk.Label(status_frame, text="启动状态: 关闭", fg="red")
        self.running_status_label.pack(anchor=tk.W)

        self.timer_status_label = tk.Label(status_frame, text="自动时间启动: 关闭", fg="red")
        self.timer_status_label.pack(anchor=tk.W)
        
        # 坐标显示
        coord_frame = tk.LabelFrame(parent, text="当前鼠标坐标")
        coord_frame.pack(fill=tk.X, pady=5)
        
        self.coord_label = tk.Label(coord_frame, text="X: 0, Y: 0", font=("Arial", 12))
        self.coord_label.pack()

    def adjust_time(self, time_type, direction):
        """调节时间（上下箭头功能）"""
        try:
            if time_type == 'start':
                current_time_str = self.start_time_var.get()
            else:
                current_time_str = self.stop_time_var.get()

            # 解析时间字符串 (YYYY-MM-DD HH:MM:SS.mmm)
            try:
                current_time = datetime.strptime(current_time_str, "%Y-%m-%d %H:%M:%S.%f")
            except ValueError:
                try:
                    # 尝试不带毫秒的格式
                    current_time = datetime.strptime(current_time_str, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    # 如果格式不对，使用当前时间
                    current_time = datetime.now()

            # 调节时间（每次调节1分钟）
            if direction > 0:
                new_time = current_time + timedelta(minutes=1)
            elif direction < 0:
                new_time = current_time - timedelta(minutes=1)
            else:
                new_time = current_time

            # 格式化新时间，确保包含毫秒
            new_time_str = new_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

            # 更新变量
            if time_type == 'start':
                self.start_time_var.set(new_time_str)
            else:
                self.stop_time_var.set(new_time_str)

        except Exception as e:
            print(f"时间调节错误: {e}")

    def monitor_mouse(self):
        """监控鼠标位置"""
        while True:
            try:
                x, y = pyautogui.position()
                self.current_pos = (x, y)
                if self.capturing:
                    self.coord_label.config(text=f"X: {x}, Y: {y}")
                time.sleep(0.1)
            except:
                pass

    def setup_hotkeys(self):
        """设置全局热键"""
        try:
            keyboard.add_hotkey(self.capture_hotkey.get().lower(), self.toggle_capture)
            keyboard.add_hotkey(self.start_hotkey.get().lower(), self.toggle_running)
        except:
            pass

    def toggle_capture(self):
        """切换捕获状态"""
        self.capturing = not self.capturing
        if self.capturing:
            self.capture_status_label.config(text="捕获状态: 开启", fg="green")
        else:
            self.capture_status_label.config(text="捕获状态: 关闭", fg="red")

    def toggle_running(self):
        """切换运行状态"""
        if not self.running:
            # 检查是否启用定时启动
            if self.timer_enabled.get():
                # 启用定时启动
                if not self.timer_running:
                    self.start_timer()
                else:
                    self.stop_timer()
            else:
                # 立即启动
                self.start_clicking()
        else:
            self.stop_clicking()
            if self.timer_running:
                self.stop_timer()

    def start_clicking(self):
        """开始点击 - 按顺序执行启用的配置项"""
        if self.running:
            return

        # 按顺序获取启用的配置（保持原有顺序1,2,3...10）
        enabled_configs = []
        for i, widget in enumerate(self.config_widgets):
            if widget['enabled_var'].get():
                try:
                    x_str = widget['x_entry'].get().strip()
                    y_str = widget['y_entry'].get().strip()

                    if not x_str or not y_str:
                        messagebox.showerror("错误", f"配置{i+1}的坐标不能为空")
                        return

                    x = int(x_str)
                    y = int(y_str)
                    action = widget['action_var'].get()
                    delay = float(widget['delay_entry'].get() or 0.2)

                    enabled_configs.append({
                        'index': i+1,  # 记录配置项编号
                        'x': x,
                        'y': y,
                        'action': action,
                        'delay': delay
                    })
                except ValueError:
                    messagebox.showerror("错误", f"配置{i+1}的参数格式错误，请检查坐标和延迟时间")
                    return

        if not enabled_configs:
            messagebox.showwarning("警告", "没有启用的配置项")
            return

        # 显示将要执行的配置
        config_info = "将按以下顺序执行:\n"
        for config in enabled_configs:
            config_info += f"配置{config['index']}: ({config['x']}, {config['y']}) - {config['action']}\n"
        print(config_info)

        self.running = True
        self.running_status_label.config(text="启动状态: 开启", fg="green")

        # 启动点击线程
        click_thread = threading.Thread(target=self.click_worker, args=(enabled_configs,), daemon=True)
        click_thread.start()

    def stop_clicking(self):
        """停止点击"""
        self.running = False
        self.running_status_label.config(text="启动状态: 关闭", fg="red")
        print("点击执行已停止")

    def click_worker(self, configs):
        """点击工作线程 - 按顺序执行选中的配置项"""
        cycle_count = 0

        # 循环逻辑：打勾=执行指定次数，不打勾=无限循环
        if self.repeat_enabled.get():
            # 打勾了循环设置，执行指定次数
            max_cycles = self.repeat_count.get()
            print(f"开始执行，总共 {max_cycles} 次循环")
        else:
            # 没有打勾，无限循环
            max_cycles = 0  # 0表示无限循环
            print("开始执行，无限循环模式")

        while self.running:
            cycle_count += 1
            print(f"第 {cycle_count} 次循环开始")

            # 按顺序执行每个启用的配置项
            for config in configs:
                if not self.running:
                    break

                try:
                    print(f"执行配置{config['index']}: 坐标({config['x']}, {config['y']}), 动作: {config['action']}")

                    # 移动到指定位置并点击
                    pyautogui.moveTo(config['x'], config['y'])
                    time.sleep(0.1)  # 短暂延迟确保移动完成

                    if config['action'] == '左键单击':
                        pyautogui.click(button='left')
                    else:  # 右键单击
                        pyautogui.click(button='right')

                    # 执行完一个动作后延迟
                    time.sleep(config['delay'])

                except Exception as e:
                    print(f"点击错误: {e}")

            # 检查是否需要停止循环
            if self.repeat_enabled.get() and cycle_count >= max_cycles:
                print(f"完成 {max_cycles} 次循环，停止执行")
                break

        # 执行完成后停止
        self.stop_clicking()

    def timer_worker(self):
        """定时器工作线程"""
        try:
            start_time_str = self.start_time_var.get()
            stop_time_str = self.stop_time_var.get()

            # 解析时间
            start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S.%f")
            stop_time = datetime.strptime(stop_time_str, "%Y-%m-%d %H:%M:%S.%f")

            print(f"定时器启动: 开始时间 {start_time_str}, 结束时间 {stop_time_str}")

            # 等待到启动时间
            while self.timer_running:
                current_time = datetime.now()

                if current_time >= start_time and not self.running:
                    print("到达启动时间，开始执行")
                    self.start_clicking()
                    break
                elif current_time >= stop_time:
                    print("已超过启动时间，定时器停止")
                    self.stop_timer()
                    return

                time.sleep(1)  # 每秒检查一次

            # 等待到停止时间
            while self.timer_running and self.running:
                current_time = datetime.now()

                if current_time >= stop_time:
                    print("到达停止时间，停止执行")
                    self.stop_clicking()
                    self.stop_timer()
                    break

                time.sleep(1)  # 每秒检查一次

        except Exception as e:
            print(f"定时器错误: {e}")
            self.stop_timer()

    def start_timer(self):
        """启动定时器"""
        if self.timer_running:
            return

        self.timer_running = True
        self.timer_status_label.config(text="自动时间启动: 开启", fg="green")

        # 启动定时器线程
        self.timer_thread = threading.Thread(target=self.timer_worker, daemon=True)
        self.timer_thread.start()
        print("定时器已启动")

    def stop_timer(self):
        """停止定时器"""
        self.timer_running = False
        self.timer_status_label.config(text="自动时间启动: 关闭", fg="red")
        print("定时器已停止")

    def clear_config(self):
        """清空配置"""
        for widget in self.config_widgets:
            widget['enabled_var'].set(False)
            widget['x_entry'].delete(0, tk.END)
            widget['y_entry'].delete(0, tk.END)
            widget['action_var'].set("左键单击")
            widget['delay_entry'].delete(0, tk.END)
            widget['delay_entry'].insert(0, "0.2")

    def save_config(self):
        """保存配置"""
        config_data = {
            'configs': [],
            'repeat_enabled': self.repeat_enabled.get(),
            'repeat_count': self.repeat_count.get(),
            'timer_enabled': self.timer_enabled.get(),
            'capture_hotkey': self.capture_hotkey.get(),
            'start_hotkey': self.start_hotkey.get(),
            'start_time': self.start_time_var.get(),
            'stop_time': self.stop_time_var.get()
        }

        for widget in self.config_widgets:
            config_data['configs'].append({
                'enabled': widget['enabled_var'].get(),
                'x': widget['x_entry'].get(),
                'y': widget['y_entry'].get(),
                'action': widget['action_var'].get(),
                'delay': widget['delay_entry'].get()
            })

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def load_config(self):
        """加载配置"""
        if not os.path.exists(self.config_file):
            return

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 恢复配置项
            configs = config_data.get('configs', [])
            for i, (widget, config) in enumerate(zip(self.config_widgets, configs)):
                widget['enabled_var'].set(config.get('enabled', False))
                widget['x_entry'].delete(0, tk.END)
                widget['x_entry'].insert(0, str(config.get('x', '')))
                widget['y_entry'].delete(0, tk.END)
                widget['y_entry'].insert(0, str(config.get('y', '')))
                widget['action_var'].set(config.get('action', '左键单击'))
                widget['delay_entry'].delete(0, tk.END)
                widget['delay_entry'].insert(0, str(config.get('delay', '0.2')))

            # 恢复其他设置
            self.repeat_enabled.set(config_data.get('repeat_enabled', False))
            self.repeat_count.set(config_data.get('repeat_count', 1))
            self.timer_enabled.set(config_data.get('timer_enabled', False))
            self.capture_hotkey.set(config_data.get('capture_hotkey', 'F1'))
            self.start_hotkey.set(config_data.get('start_hotkey', 'F8'))
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            self.start_time_var.set(config_data.get('start_time', current_time))
            self.stop_time_var.set(config_data.get('stop_time', current_time))

        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")

    def run(self):
        """运行程序"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            pass

    def on_closing(self):
        """程序关闭时的处理"""
        self.running = False
        self.capturing = False
        try:
            keyboard.unhook_all()
        except:
            pass
        self.root.destroy()

if __name__ == "__main__":
    # 设置pyautogui安全设置
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.1

    app = AutoClicker()
    app.run()
