# -*- coding: utf-8 -*-
"""
界面演示脚本 - 展示优化后的布局
"""
import tkinter as tk
from tkinter import ttk
from datetime import datetime

def create_demo_window():
    root = tk.Tk()
    root.title("屏幕自动点击器 - 界面演示")
    root.geometry("900x650")
    root.resizable(False, False)
    
    # 主框架
    main_frame = tk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 左侧设置区
    left_frame = tk.LabelFrame(main_frame, text="设置区", width=580)
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
    left_frame.pack_propagate(False)
    
    # 右侧控制区
    right_frame = tk.LabelFrame(main_frame, text="控制区", width=300)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=(5, 0))
    right_frame.pack_propagate(False)
    
    # 左侧配置项
    for i in range(10):
        frame = tk.Frame(left_frame, relief=tk.RIDGE, bd=1, height=35)
        frame.pack(fill=tk.X, pady=2, padx=5)
        frame.pack_propagate(False)
        
        # 启用复选框 - 调整宽度让数字可见
        enabled_var = tk.BooleanVar()
        if i in [0, 2, 4]:  # 演示：启用1、3、5
            enabled_var.set(True)
        enabled_cb = tk.Checkbutton(frame, text=f"启用{i+1}", variable=enabled_var, width=10)
        enabled_cb.place(x=5, y=5)

        # 坐标输入 - 调整位置
        tk.Label(frame, text="坐标:").place(x=90, y=8)
        x_entry = tk.Entry(frame, width=8)
        x_entry.place(x=125, y=6)
        if i in [0, 2, 4]:  # 演示数据
            x_entry.insert(0, str(100 + i * 50))

        tk.Label(frame, text=",").place(x=185, y=8)
        y_entry = tk.Entry(frame, width=8)
        y_entry.place(x=195, y=6)
        if i in [0, 2, 4]:  # 演示数据
            y_entry.insert(0, str(200 + i * 30))

        # 动作选择 - 调整位置
        tk.Label(frame, text="动作:").place(x=260, y=8)
        action_var = tk.StringVar(value="左键单击")
        action_combo = ttk.Combobox(frame, textvariable=action_var,
                                  values=["左键单击", "右键单击"], width=10, state="readonly")
        action_combo.place(x=295, y=6)

        # 延迟设置 - 调整位置，"秒"字往右移
        tk.Label(frame, text="延迟:").place(x=420, y=8)
        delay_entry = tk.Entry(frame, width=6)
        delay_entry.place(x=450, y=6)
        delay_entry.insert(0, "0.2")
        tk.Label(frame, text="秒").place(x=510, y=8)  # 往右移动
    
    # 右侧控制区
    # 自动/手动控制
    control_frame = tk.LabelFrame(right_frame, text="自动/手动")
    control_frame.pack(fill=tk.X, pady=5)
    
    # 捕获开关
    capture_frame = tk.Frame(control_frame)
    capture_frame.pack(fill=tk.X, pady=2)
    tk.Label(capture_frame, text="捕获开关:").pack(side=tk.LEFT)
    capture_combo = ttk.Combobox(capture_frame, value="F1",
                               values=[f"F{i}" for i in range(1, 13)], width=5, state="readonly")
    capture_combo.pack(side=tk.LEFT, padx=5)
    tk.Button(capture_frame, text="清空配置区").pack(side=tk.RIGHT)
    
    # 启动开关
    start_frame = tk.Frame(control_frame)
    start_frame.pack(fill=tk.X, pady=2)
    tk.Label(start_frame, text="启动开关:").pack(side=tk.LEFT)
    start_combo = ttk.Combobox(start_frame, value="F8",
                             values=[f"F{i}" for i in range(1, 13)], width=5, state="readonly")
    start_combo.pack(side=tk.LEFT, padx=5)
    tk.Button(start_frame, text="保存配置区").pack(side=tk.RIGHT)
    
    # 设置区
    settings_frame = tk.LabelFrame(right_frame, text="设置")
    settings_frame.pack(fill=tk.X, pady=5)
    
    # 循环设置
    repeat_frame = tk.Frame(settings_frame)
    repeat_frame.pack(fill=tk.X, pady=2)
    repeat_var = tk.BooleanVar(value=True)
    tk.Checkbutton(repeat_frame, text="循环设置:", variable=repeat_var).pack(side=tk.LEFT)
    tk.Label(repeat_frame, text="重复执行").pack(side=tk.LEFT, padx=(10, 0))
    
    # 执行次数设置 - 修复显示问题
    count_frame = tk.Frame(repeat_frame)
    count_frame.pack(side=tk.LEFT, padx=(10, 0))
    tk.Label(count_frame, text="执行次数").pack(side=tk.LEFT)
    repeat_count = tk.IntVar(value=5)
    repeat_spinbox = tk.Spinbox(count_frame, from_=1, to=9999, textvariable=repeat_count,
                               width=6, state="normal")
    repeat_spinbox.pack(side=tk.LEFT, padx=5)
    tk.Label(count_frame, text="次").pack(side=tk.LEFT)
    
    # 定时启动
    timer_frame = tk.Frame(settings_frame)
    timer_frame.pack(fill=tk.X, pady=2)
    timer_var = tk.BooleanVar()
    tk.Checkbutton(timer_frame, text="定时启动:", variable=timer_var).pack(anchor=tk.W)
    
    # 启动时间
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    start_time_frame = tk.Frame(settings_frame)
    start_time_frame.pack(fill=tk.X, pady=2)
    tk.Label(start_time_frame, text="启动时间:").pack(side=tk.LEFT)
    start_time_var = tk.StringVar(value=current_time)
    start_time_entry = tk.Entry(start_time_frame, textvariable=start_time_var, width=25)
    start_time_entry.pack(side=tk.LEFT, padx=5)
    
    # 启动时间调节按钮
    start_btn_frame = tk.Frame(start_time_frame)
    start_btn_frame.pack(side=tk.LEFT, padx=3)
    start_up_btn = tk.Button(start_btn_frame, text="▲", width=3, height=1)
    start_up_btn.pack(side=tk.TOP)
    start_down_btn = tk.Button(start_btn_frame, text="▼", width=3, height=1)
    start_down_btn.pack(side=tk.TOP)

    # 关闭时间
    stop_time_frame = tk.Frame(settings_frame)
    stop_time_frame.pack(fill=tk.X, pady=2)
    tk.Label(stop_time_frame, text="关闭时间:").pack(side=tk.LEFT)
    stop_time_var = tk.StringVar(value=current_time)
    stop_time_entry = tk.Entry(stop_time_frame, textvariable=stop_time_var, width=25)
    stop_time_entry.pack(side=tk.LEFT, padx=5)

    # 关闭时间调节按钮
    stop_btn_frame = tk.Frame(stop_time_frame)
    stop_btn_frame.pack(side=tk.LEFT, padx=3)
    stop_up_btn = tk.Button(stop_btn_frame, text="▲", width=3, height=1)
    stop_up_btn.pack(side=tk.TOP)
    stop_down_btn = tk.Button(stop_btn_frame, text="▼", width=3, height=1)
    stop_down_btn.pack(side=tk.TOP)
    
    # 状态显示
    status_frame = tk.LabelFrame(right_frame, text="状态")
    status_frame.pack(fill=tk.X, pady=5)
    
    capture_status_label = tk.Label(status_frame, text="捕获状态: 关闭", fg="red")
    capture_status_label.pack(anchor=tk.W)

    running_status_label = tk.Label(status_frame, text="启动状态: 关闭", fg="red")
    running_status_label.pack(anchor=tk.W)

    timer_status_label = tk.Label(status_frame, text="自动时间启动: 关闭", fg="red")
    timer_status_label.pack(anchor=tk.W)
    
    # 坐标显示
    coord_frame = tk.LabelFrame(right_frame, text="当前鼠标坐标")
    coord_frame.pack(fill=tk.X, pady=5)
    
    coord_label = tk.Label(coord_frame, text="X: 1080, Y: 590", font=("Arial", 12))
    coord_label.pack()
    
    return root

if __name__ == "__main__":
    demo_window = create_demo_window()
    demo_window.mainloop()
