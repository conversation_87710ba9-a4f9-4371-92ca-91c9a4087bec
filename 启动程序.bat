@echo off
chcp 65001 >nul
echo 屏幕自动点击器启动中...
echo.
echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

echo 正在检查依赖包...
pip show pyautogui >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

echo 启动程序...
python auto_clicker.py

if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查错误信息
    pause
)
