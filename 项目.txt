帮我写一个获取屏幕坐标，自动轮番鼠标点击
1.界面左边设置区设置10个，启用 坐标：（）动作（） 延迟：（）秒。如果打勾以后就按顺序执行。其中动作就分左键单击跟右键单击
2.界面右边启动\关闭，  捕获开关：（）清空配置去   启动开关（）保存配置区。捕获开关打开，鼠标移动动屏幕那个位置，界面给个地方显示坐标的位置。清空配置就是把左边设置区的10个全部清零，如坐标，延迟时间。保存配置就把左边设置区的全部保存，（每次开启软件的时候就自动加载这个配置）。捕获开关跟启动开关可以下拉，设置全局热键（F1-F12）由用户可以选择下拉
3.循环设置：（复选项）重复执行，如果用户打勾了重复执行，那么按下启动开关热键以后，就一直重复执行，重复执行的次数就是下面用户设置的执行次数。
4.定时启动：(复选项）定时启动。启动时间，关闭时间。设置好以后，如果用户打勾了。不管启动开关按还是没按，到了启动时间就启动，到了关闭时间就关闭，一直重复左边设置区中打勾的。这里启停时间设置要到毫秒级别，不能直到秒
5.捕获状态（关闭），启动状态（关闭）。这里提示用户软件的状态情况。
